{"spider": "https://github.com/custom_spider.jar", "sites": [{"key": "one", "name": "One", "type": 3, "api": "csp_Csp", "searchable": 1, "changeable": 1, "ext": "https://github.com/one.json"}, {"key": "two", "name": "Two", "type": 3, "api": "csp_Csp", "searchable": 1, "changeable": 1, "ext": "https://github.com/two.json"}, {"key": "extend", "name": "Extend", "type": 3, "api": "csp_Csp", "searchable": 1, "changeable": 1, "ext": "https://github.com/extend.json", "jar": "https://github.com/extend.jar"}], "parses": [{"name": "官方", "type": 1, "url": "https://google.com/api/?url="}], "doh": [{"name": "Google", "url": "https://dns.google/dns-query", "ips": ["*******", "*******"]}], "headers": [{"host": "gslbserv.itv.cmvideo.cn", "header": {"User-Agent": "okhttp/3.12.13"}}], "proxy": ["raw.githubusercontent.com"], "hosts": ["cache.ott.ystenlive.itv.cmvideo.cn=base-v4-free-mghy.e.cdn.chinamobile.com"], "flags": ["qq"], "ads": ["static-mozai.4gtv.tv"]}