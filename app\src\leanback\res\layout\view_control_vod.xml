<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_controller"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingTop="18dp"
    android:paddingEnd="16dp"
    android:paddingBottom="8dp">

    <com.fongmi.android.tv.ui.custom.CustomHorizontalGridView
        android:id="@+id/parse"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:visibility="gone"
        tools:layout_height="36dp" />

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/actionLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusLeft="@id/loop"
                android:nextFocusDown="@id/timeBar"
                android:text="@string/play_next"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/prev"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:text="@string/play_prev"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/reset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="刷新" />

            <TextView
                android:id="@+id/change2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:text="@string/play_change"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="換源" />

            <TextView
                android:id="@+id/player"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:text="@string/play_exo"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/decode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="硬解" />

            <com.fongmi.android.tv.ui.custom.CustomUpDownView
                android:id="@+id/speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="1.00" />

            <TextView
                android:id="@+id/scale"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="預設" />

            <com.fongmi.android.tv.ui.custom.CustomUpDownView
                android:id="@+id/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:tag="3"
                android:text="@string/play_track_text"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/audio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:tag="1"
                android:text="@string/play_track_audio"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/video"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:tag="2"
                android:text="@string/play_track_video"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.fongmi.android.tv.ui.custom.CustomUpDownView
                android:id="@+id/opening"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="00:00" />

            <com.fongmi.android.tv.ui.custom.CustomUpDownView
                android:id="@+id/ending"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="00:00" />

            <TextView
                android:id="@+id/danmaku"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/timeBar"
                android:text="@string/danmaku"
                android:textColor="@color/text"
                android:textSize="14sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/loop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/selector_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusRight="@id/next"
                android:nextFocusDown="@id/timeBar"
                android:text="@string/play_loop"
                android:textColor="@color/text"
                android:textSize="14sp" />

        </LinearLayout>
    </HorizontalScrollView>

    <com.fongmi.android.tv.ui.custom.CustomSeekView
        android:id="@+id/seek"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

</LinearLayout>