<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:keepScreenOn="true"
    tools:ignore="NestedWeights">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/vod"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="0.3"
                    android:text="@string/setting_vod"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/vodUrl"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.7"
                    android:ellipsize="middle"
                    android:gravity="end"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:ignore="NestedWeights"
                    tools:text="https://" />

            </LinearLayout>

            <ImageView
                android:id="@+id/vodHome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_setting_home" />

            <ImageView
                android:id="@+id/vodHistory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_setting_history" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/live"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="0.3"
                    android:text="@string/setting_live"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/liveUrl"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.7"
                    android:ellipsize="middle"
                    android:gravity="end"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:ignore="NestedWeights"
                    tools:text="https://" />

            </LinearLayout>

            <ImageView
                android:id="@+id/liveHome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_setting_home" />

            <ImageView
                android:id="@+id/liveHistory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_setting_history" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/wall"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/player"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="0.3"
                    android:text="@string/setting_wall"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/wallUrl"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.7"
                    android:ellipsize="middle"
                    android:gravity="end"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:ignore="NestedWeights"
                    tools:text="https://" />

            </LinearLayout>

            <ImageView
                android:id="@+id/wallDefault"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/player"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_setting_home" />

            <ImageView
                android:id="@+id/wallRefresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:nextFocusDown="@id/player"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_setting_refresh" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/player"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/selector_item"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/setting_player"
                android:textColor="@color/white"
                android:textSize="18sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/incognito"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/selector_item"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusDown="@id/quality"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:text="@string/setting_incognito"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/incognitoText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:textColor="@color/white"
                android:textSize="18sp"
                tools:text="Off" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/quality"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_quality"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/qualityText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:text="High" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/size"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_size"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/sizeText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:text="Medium" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/doh"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_doh"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/dohText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:text="Google" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/proxy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_proxy"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/proxyText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:text="http" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/backup"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/setting_backup"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/restore"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/setting_restore"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/cache"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_cache"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/cacheText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:text="1.0 MB" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/version"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/selector_item"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:text="@string/setting_version"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/versionText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    tools:text="1.2.1" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>