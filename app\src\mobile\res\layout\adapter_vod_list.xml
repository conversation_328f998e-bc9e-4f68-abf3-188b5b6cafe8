<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:background="@drawable/shape_vod_list"
    android:foreground="@drawable/shape_vod"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="12dp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="12dp"
        android:scaleType="fitCenter"
        app:shapeAppearanceOverlay="@style/Vod.Grid"
        tools:src="@drawable/ic_img_error" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="蜘蛛人" />

        <TextView
            android:id="@+id/remark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="1080p" />

    </LinearLayout>
</LinearLayout>