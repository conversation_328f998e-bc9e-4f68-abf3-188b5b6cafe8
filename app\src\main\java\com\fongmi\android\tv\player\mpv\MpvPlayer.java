package com.fongmi.android.tv.player.mpv;

import android.content.Context;
import android.net.Uri;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;

import androidx.media3.common.VideoSize;

import com.fongmi.android.tv.bean.Drm;
import com.fongmi.android.tv.bean.Sub;

import java.util.List;
import java.util.Map;

/**
 * MPV播放器实现类
 * 提供与ExoPlayer相同的接口，用于播放视频内容
 * 使用libmpv native库进行视频播放
 */
public class MpvPlayer implements SurfaceHolder.Callback {

    static {
        try {
            System.loadLibrary("mpv");
        } catch (UnsatisfiedLinkError e) {
            // libmpv库未找到，MPV播放器将无法工作
            e.printStackTrace();
        }
    }

    public interface Listener {
        void onVideoSizeChanged(VideoSize videoSize);
        void onPlaybackStateChanged(int playbackState);
        void onPlayerError(Exception error);
        void onPositionDiscontinuity();
        void onPlayWhenReadyChanged(boolean playWhenReady);
        void onIsPlayingChanged(boolean isPlaying);
    }

    // 播放状态常量
    public static final int STATE_IDLE = 1;
    public static final int STATE_BUFFERING = 2;
    public static final int STATE_READY = 3;
    public static final int STATE_ENDED = 4;

    private Context context;
    private SurfaceView surfaceView;
    private SurfaceHolder surfaceHolder;
    private Listener listener;

    private boolean playWhenReady = false;
    private boolean isPlaying = false;
    private int playbackState = STATE_IDLE;
    private long duration = 0;
    private long position = 0;
    private float playbackSpeed = 1.0f;
    private VideoSize videoSize = VideoSize.UNKNOWN;

    private String currentUrl;
    private Map<String, String> currentHeaders;

    // MPV native handle
    private long mpvHandle = 0;

    public MpvPlayer(Context context) {
        this.context = context;
        initializeMpv();
    }

    private void initializeMpv() {
        try {
            mpvHandle = nativeCreate();
            if (mpvHandle != 0) {
                nativeInit(mpvHandle);
                // 设置一些基本的MPV选项
                nativeSetOptionString(mpvHandle, "vo", "gpu");
                nativeSetOptionString(mpvHandle, "hwdec", "auto");
                nativeSetOptionString(mpvHandle, "audio-client-name", "TV");
            }
        } catch (Exception e) {
            e.printStackTrace();
            notifyError(e);
        }
    }

    public void setVideoSurfaceView(SurfaceView surfaceView) {
        this.surfaceView = surfaceView;
        if (surfaceView != null) {
            surfaceHolder = surfaceView.getHolder();
            surfaceHolder.addCallback(this);
        }
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public void setMediaItem(Map<String, String> headers, Uri uri, String format, Drm drm, List<Sub> subs, int decode) {
        this.currentUrl = uri.toString();
        this.currentHeaders = headers;

        if (mpvHandle != 0) {
            try {
                // 设置HTTP headers
                if (headers != null && !headers.isEmpty()) {
                    StringBuilder headerString = new StringBuilder();
                    for (Map.Entry<String, String> entry : headers.entrySet()) {
                        headerString.append(entry.getKey()).append(": ").append(entry.getValue()).append("\r\n");
                    }
                    nativeSetOptionString(mpvHandle, "http-header-fields", headerString.toString());
                }

                // 加载文件
                nativeCommand(mpvHandle, new String[]{"loadfile", currentUrl});
                notifyPlaybackStateChanged(STATE_BUFFERING);
            } catch (Exception e) {
                e.printStackTrace();
                notifyError(e);
            }
        }
    }

    public void prepare() {
        // MPV会自动准备播放，这里只是状态更新
        if (mpvHandle != 0) {
            notifyPlaybackStateChanged(STATE_READY);
        }
    }

    public void play() {
        playWhenReady = true;
        if (mpvHandle != 0 && playbackState == STATE_READY) {
            try {
                nativeSetPropertyBoolean(mpvHandle, "pause", false);
                isPlaying = true;
                notifyPlayWhenReadyChanged(true);
                notifyIsPlayingChanged(true);
            } catch (Exception e) {
                e.printStackTrace();
                notifyError(e);
            }
        }
    }

    public void pause() {
        playWhenReady = false;
        if (mpvHandle != 0) {
            try {
                nativeSetPropertyBoolean(mpvHandle, "pause", true);
                isPlaying = false;
                notifyPlayWhenReadyChanged(false);
                notifyIsPlayingChanged(false);
            } catch (Exception e) {
                e.printStackTrace();
                notifyError(e);
            }
        }
    }

    public void stop() {
        playWhenReady = false;
        isPlaying = false;
        playbackState = STATE_IDLE;
        if (mpvHandle != 0) {
            try {
                nativeCommand(mpvHandle, new String[]{"stop"});
                notifyPlaybackStateChanged(STATE_IDLE);
                notifyPlayWhenReadyChanged(false);
                notifyIsPlayingChanged(false);
            } catch (Exception e) {
                e.printStackTrace();
                notifyError(e);
            }
        }
    }

    public void seekTo(long positionMs) {
        this.position = positionMs;
        if (mpvHandle != 0) {
            try {
                double positionSec = positionMs / 1000.0;
                nativeSetPropertyDouble(mpvHandle, "time-pos", positionSec);
                if (listener != null) {
                    listener.onPositionDiscontinuity();
                }
            } catch (Exception e) {
                e.printStackTrace();
                notifyError(e);
            }
        }
    }

    public void release() {
        stop();
        if (surfaceHolder != null) {
            surfaceHolder.removeCallback(this);
        }
        if (mpvHandle != 0) {
            try {
                nativeDestroy(mpvHandle);
                mpvHandle = 0;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public boolean getPlayWhenReady() {
        return playWhenReady;
    }

    public void setPlayWhenReady(boolean playWhenReady) {
        this.playWhenReady = playWhenReady;
        if (playWhenReady && playbackState == STATE_READY) {
            play();
        } else {
            pause();
        }
    }

    public int getPlaybackState() {
        return playbackState;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public long getDuration() {
        return duration;
    }

    public long getCurrentPosition() {
        return position;
    }

    public void setPlaybackSpeed(float speed) {
        this.playbackSpeed = speed;
        if (mpvHandle != 0) {
            try {
                nativeSetPropertyDouble(mpvHandle, "speed", speed);
            } catch (Exception e) {
                e.printStackTrace();
                notifyError(e);
            }
        }
    }

    public float getPlaybackSpeed() {
        return playbackSpeed;
    }

    public VideoSize getVideoSize() {
        return videoSize;
    }

    public boolean isCurrentMediaItemLive() {
        if (mpvHandle != 0) {
            try {
                return nativeGetPropertyBoolean(mpvHandle, "demuxer-via-network");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    // SurfaceHolder.Callback 实现
    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        if (mpvHandle != 0 && holder != null) {
            try {
                Surface surface = holder.getSurface();
                nativeSetSurface(mpvHandle, surface);
            } catch (Exception e) {
                e.printStackTrace();
                notifyError(e);
            }
        }
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        if (mpvHandle != 0) {
            try {
                // MPV会自动处理surface尺寸变化
                notifyVideoSizeChanged(width, height);
            } catch (Exception e) {
                e.printStackTrace();
                notifyError(e);
            }
        }
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        if (mpvHandle != 0) {
            try {
                nativeSetSurface(mpvHandle, null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // 通知方法
    private void notifyPlaybackStateChanged(int state) {
        this.playbackState = state;
        if (listener != null) {
            listener.onPlaybackStateChanged(state);
        }
    }

    private void notifyPlayWhenReadyChanged(boolean playWhenReady) {
        if (listener != null) {
            listener.onPlayWhenReadyChanged(playWhenReady);
        }
    }

    private void notifyIsPlayingChanged(boolean isPlaying) {
        if (listener != null) {
            listener.onIsPlayingChanged(isPlaying);
        }
    }

    private void notifyVideoSizeChanged(int width, int height) {
        this.videoSize = new VideoSize(width, height);
        if (listener != null) {
            listener.onVideoSizeChanged(videoSize);
        }
    }

    private void notifyError(Exception error) {
        if (listener != null) {
            listener.onPlayerError(error);
        }
    }

    // Native方法声明
    private native long nativeCreate();
    private native void nativeInit(long handle);
    private native void nativeDestroy(long handle);
    private native void nativeSetSurface(long handle, Surface surface);
    private native void nativeSetOptionString(long handle, String name, String value);
    private native void nativeSetPropertyBoolean(long handle, String name, boolean value);
    private native void nativeSetPropertyDouble(long handle, String name, double value);
    private native boolean nativeGetPropertyBoolean(long handle, String name);
    private native double nativeGetPropertyDouble(long handle, String name);
    private native void nativeCommand(long handle, String[] args);
}
