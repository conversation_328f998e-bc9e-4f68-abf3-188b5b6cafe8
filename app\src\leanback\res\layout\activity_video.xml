<?xml version="1.0" encoding="utf-8"?>
<com.fongmi.android.tv.ui.custom.ProgressLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/progressLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/video"
        android:layout_width="400dp"
        android:layout_height="225dp"
        android:layout_margin="24dp"
        android:background="@color/black"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:foreground="@drawable/selector_video"
        android:nextFocusLeft="@id/video"
        android:nextFocusRight="@id/desc"
        android:nextFocusUp="@id/video"
        android:nextFocusDown="@id/flag">

        <androidx.media3.ui.PlayerView
            android:id="@+id/exo"
            style="@style/Player.Vod"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:surface_type="surface_view" />

        <master.flame.danmaku.ui.widget.DanmakuView
            android:id="@+id/danmaku"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <include
            android:id="@+id/widget"
            layout="@layout/view_widget_vod"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <include
            android:id="@+id/control"
            layout="@layout/view_control_vod"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:visibility="gone" />

    </FrameLayout>

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        android:layout_toEndOf="@+id/video"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="24sp"
        android:textStyle="bold"
        tools:text="慶餘年第二季" />

    <TextView
        android:id="@+id/remark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/name"
        android:layout_alignStart="@+id/name"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="24dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="更新到第二季" />

    <LinearLayout
        android:id="@+id/row1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/remark"
        android:layout_alignStart="@+id/remark"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="24dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/site"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="站源：泥巴" />

        <TextView
            android:id="@+id/year"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="年份：2022" />

        <TextView
            android:id="@+id/area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="地區：台灣" />

        <TextView
            android:id="@+id/type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="類型：科幻" />

    </LinearLayout>

    <TextView
        android:id="@+id/director"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/row1"
        android:layout_alignStart="@+id/name"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="24dp"
        android:nextFocusDown="@id/actor"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="導演：FongMi" />

    <TextView
        android:id="@+id/actor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/director"
        android:layout_alignStart="@+id/name"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="24dp"
        android:nextFocusDown="@id/content"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="演員：FongMi" />

    <TextView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/actor"
        android:layout_alignStart="@+id/name"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="24dp"
        android:ellipsize="end"
        android:lineSpacingExtra="4dp"
        android:maxLines="1"
        android:nextFocusDown="@id/desc"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="簡介：" />

    <LinearLayout
        android:id="@+id/row2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignStart="@+id/name"
        android:layout_alignBottom="@+id/video"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:background="@drawable/selector_item"
            android:drawablePadding="6dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:nextFocusDown="@id/flag"
            android:singleLine="true"
            android:text="@string/detail_desc"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:drawableStartCompat="@drawable/ic_detail_desc" />

        <TextView
            android:id="@+id/keep"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:background="@drawable/selector_item"
            android:drawablePadding="6dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:nextFocusDown="@id/flag"
            android:singleLine="true"
            android:text="@string/keep"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:drawableStartCompat="@drawable/ic_detail_keep_off" />

        <TextView
            android:id="@+id/change1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/selector_item"
            android:drawablePadding="6dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:nextFocusRight="@id/change1"
            android:nextFocusDown="@id/flag"
            android:singleLine="true"
            android:text="@string/play_change"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:drawableStartCompat="@drawable/ic_detail_change" />

    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/video"
        android:overScrollMode="never">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingBottom="8dp">

            <com.fongmi.android.tv.ui.custom.CustomHorizontalGridView
                android:id="@+id/flag"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.fongmi.android.tv.ui.custom.CustomHorizontalGridView
                android:id="@+id/quality"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.fongmi.android.tv.ui.custom.CustomHorizontalGridView
                android:id="@+id/episode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:visibility="gone" />

            <com.fongmi.android.tv.ui.custom.CustomHorizontalGridView
                android:id="@+id/array"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:visibility="gone" />

            <com.fongmi.android.tv.ui.custom.CustomHorizontalGridView
                android:id="@+id/part"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:visibility="gone" />

            <androidx.leanback.widget.HorizontalGridView
                android:id="@+id/quick"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:visibility="gone" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</com.fongmi.android.tv.ui.custom.ProgressLayout>