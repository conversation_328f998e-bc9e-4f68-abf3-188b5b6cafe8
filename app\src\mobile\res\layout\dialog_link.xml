<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="24dp"
    android:paddingTop="16dp"
    android:paddingEnd="24dp">

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/dialog_config_url"
        app:endIconDrawable="@drawable/ic_action_choose"
        app:endIconMode="custom">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:imeOptions="actionDone"
            android:importantForAutofill="no"
            android:inputType="textUri"
            android:singleLine="true" />

    </com.google.android.material.textfield.TextInputLayout>
</LinearLayout>