<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/app_history"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/sync"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_action_sync" />

        <ImageView
            android:id="@+id/delete"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_action_delete"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="-8dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingBottom="8dp" />

    </FrameLayout>
</LinearLayout>