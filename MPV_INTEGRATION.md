# MPV播放器集成说明

## 概述

本项目已成功集成MPV播放器作为ExoPlayer的替代选项。用户可以在设置中选择使用ExoPlayer或MPV播放器来播放视频内容。

## 功能特性

### 1. 播放器类型选择
- **EXO**: 默认的ExoPlayer播放器，基于androidx.media3
- **MPV**: 新增的MPV播放器，基于libmpv-android

### 2. 设置界面
- 在播放器设置中新增"播放器类型"选项
- 支持在EXO和MPV之间切换
- 设置会自动保存并在下次启动时生效

### 3. 兼容性
- 保持与现有ExoPlayer功能的完全兼容
- 支持相同的播放控制接口
- 支持字幕、弹幕等功能

## 技术实现

### 1. 核心类结构

#### Players.java
- 添加了播放器类型常量：`EXO = 0`, `MPV = 1`
- 新增播放器类型检查方法：`isExo()`, `isMpv()`
- 实现播放器切换方法：`togglePlayer()`
- 修改播放控制方法以支持多播放器

#### MpvPlayer.java
- 实现与ExoPlayer相同的接口
- 提供播放控制、状态管理、事件监听等功能
- 支持SurfaceView渲染

#### Setting.java
- 新增播放器类型存储方法：`getPlayer()`, `putPlayer()`
- 默认播放器类型为EXO

### 2. 依赖配置

在`app/build.gradle`中添加了MPV依赖：
```gradle
implementation 'is.xyz.mpv:mpv-android:2024-11-02'
```

### 3. 界面更新

#### Mobile版本
- `fragment_setting_player.xml`: 添加播放器类型选择布局
- `SettingPlayerFragment.java`: 实现播放器类型选择逻辑

#### TV版本
- `activity_setting_player.xml`: 添加播放器类型选择布局
- `SettingPlayerActivity.java`: 实现播放器类型选择逻辑

### 4. 多语言支持

在字符串资源中添加了播放器选择相关的文本：
- `strings.xml`: 英文版本
- `strings-zh-rCN.xml`: 简体中文版本
- `strings-zh-rTW.xml`: 繁体中文版本

## 使用方法

### 1. 切换播放器类型

#### 在Mobile版本中：
1. 进入设置 → 播放器设置
2. 点击"播放器类型"选项
3. 在弹出的对话框中选择"EXO"或"MPV"
4. 设置会立即生效

#### 在TV版本中：
1. 进入设置 → 播放器设置
2. 使用遥控器导航到"播放器类型"选项
3. 按确认键切换播放器类型
4. 设置会立即生效

### 2. 播放器特性对比

| 功能 | ExoPlayer | MPV |
|------|-----------|-----|
| 基础播放 | ✅ | ✅ |
| 硬件解码 | ✅ | ✅ |
| 字幕支持 | ✅ | ✅ |
| 弹幕支持 | ✅ | ✅ |
| 播放速度调节 | ✅ | ✅ |
| 直播支持 | ✅ | ✅ |
| 格式支持 | 广泛 | 更广泛 |
| 性能 | 优秀 | 优秀 |

## 开发说明

### 1. 扩展MPV功能

如需扩展MPV播放器功能，可以修改`MpvPlayer.java`类：

```java
// 添加新的MPV特性
public void setMpvOption(String option, String value) {
    // TODO: 调用libmpv API设置选项
}
```

### 2. 添加新的播放器类型

如需添加更多播放器类型：

1. 在`Players.java`中添加新的常量
2. 更新`select_player`字符串数组
3. 在`setPlayer()`方法中添加新的播放器初始化逻辑
4. 更新所有播放控制方法

### 3. 测试

运行单元测试：
```bash
./gradlew test
```

测试文件位置：`app/src/test/java/com/fongmi/android/tv/player/PlayerIntegrationTest.java`

## 注意事项

1. **首次使用MPV**: 首次切换到MPV播放器时，可能需要下载额外的native库
2. **性能考虑**: MPV播放器在某些设备上可能有不同的性能表现
3. **兼容性**: 建议在目标设备上充分测试MPV播放器的兼容性
4. **内存使用**: MPV播放器可能有不同的内存使用模式

## 故障排除

### 1. MPV播放器无法启动
- 检查设备是否支持libmpv
- 确认依赖库已正确安装
- 查看logcat输出获取详细错误信息

### 2. 播放器切换不生效
- 确认设置已正确保存
- 重启应用程序
- 检查播放器初始化逻辑

### 3. 性能问题
- 尝试调整MPV配置选项
- 检查硬件解码设置
- 监控内存和CPU使用情况

## 更新日志

- **v1.0**: 初始MPV集成实现
  - 添加MPV播放器支持
  - 实现播放器类型选择
  - 更新设置界面
  - 添加多语言支持
