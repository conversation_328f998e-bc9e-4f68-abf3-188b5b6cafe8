<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="150dp"
    android:layout_height="200dp"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_20"
        android:scaleType="center"
        app:shapeAppearanceOverlay="@style/Vod.Grid"
        tools:src="@drawable/ic_img_loading" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/delete"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_50"
        android:scaleType="center"
        android:src="@drawable/ic_vod_delete"
        android:visibility="gone"
        app:shapeAppearanceOverlay="@style/Vod.Grid"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/year"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_vod_year"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        tools:text="2022"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/site"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_vod_site"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        tools:text="泥巴"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/remark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/name"
        android:background="@drawable/shape_vod_remark"
        android:ellipsize="marquee"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="14sp"
        tools:text="1080p" />

    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shape_vod_name"
        android:ellipsize="marquee"
        android:gravity="center"
        android:padding="6dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="蜘蛛人" />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/selector_vod"
        android:duplicateParentState="true" />

</RelativeLayout>