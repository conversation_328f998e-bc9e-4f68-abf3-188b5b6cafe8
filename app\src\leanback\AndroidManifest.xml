<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />

    <uses-feature
        android:name="android.hardware.microphone"
        android:required="false" />

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application android:banner="@drawable/ic_banner">

        <meta-data
            android:name="design_width_in_dp"
            android:value="960" />

        <meta-data
            android:name="design_height_in_dp"
            android:value="540" />

        <activity
            android:name=".ui.activity.HomeActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:screenOrientation="sensorLandscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="content" />
                <data android:scheme="file" />
                <data android:mimeType="video/*" />
                <data android:mimeType="audio/*" />
                <data android:mimeType="text/plain" />
                <data android:mimeType="application/x-bittorrent" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="smb" />
                <data android:scheme="rtmp" />
                <data android:scheme="rtsp" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:mimeType="video/*" />
                <data android:mimeType="audio/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="ed2k" />
                <data android:scheme="magnet" />
                <data android:scheme="thunder" />
                <data android:scheme="jianpian" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activity.CastActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape">
            <intent-filter>
                <action android:name="${applicationId}.cast" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activity.VodActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:screenOrientation="sensorLandscape" />

        <activity
            android:name=".ui.activity.LiveActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/AppTheme.Live" />

        <activity
            android:name=".ui.activity.VideoActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboard|keyboardHidden|navigation"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <activity
            android:name=".ui.activity.KeepActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:screenOrientation="sensorLandscape" />

        <activity
            android:name=".ui.activity.PushActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:screenOrientation="sensorLandscape" />

        <activity
            android:name=".ui.activity.SearchActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:screenOrientation="sensorLandscape"
            android:windowSoftInputMode="stateHidden" />

        <activity
            android:name=".ui.activity.CollectActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:launchMode="singleTop"
            android:screenOrientation="sensorLandscape" />

        <activity
            android:name=".ui.activity.FileActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:screenOrientation="sensorLandscape" />

        <activity
            android:name=".ui.activity.SettingActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:screenOrientation="sensorLandscape" />

        <activity
            android:name=".ui.activity.SettingPlayerActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation"
            android:screenOrientation="sensorLandscape" />

        <service
            android:name="com.android.cast.dlna.dmr.DLNARendererService"
            android:exported="false" />

        <receiver
            android:name=".receiver.BootReceiver"
            android:exported="true"
            android:priority="999">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>

    </application>
</manifest>