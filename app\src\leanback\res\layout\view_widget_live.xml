<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_toStartOf="@+id/clock"
            android:ellipsize="marquee"
            android:shadowColor="@color/grey_900"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="1"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="20sp"
            tools:text="民視" />

        <TextView
            android:id="@+id/size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/title"
            android:layout_alignStart="@+id/title"
            android:shadowColor="@color/grey_900"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="1"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="1920 x 1080" />

        <TextView
            android:id="@+id/clock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:shadowColor="@color/grey_900"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="1"
            android:textColor="@color/white"
            android:textSize="20sp"
            tools:text="09:20:00" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <include layout="@layout/view_progress" />

        <TextView
            android:id="@+id/traffic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:visibility="gone"
            tools:text="120KB/s" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_widget"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_widget_error" />

        <TextView
            android:id="@+id/text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="16sp"
            tools:text="@string/error_play_url" />

    </LinearLayout>

    <TextView
        android:id="@+id/digital"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_widget"
        android:includeFontPadding="false"
        android:letterSpacing="0.05"
        android:padding="16dp"
        android:shadowColor="@color/grey_900"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="1"
        android:textColor="@color/white"
        android:textSize="64sp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="05"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_widget"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/action"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_widget_play" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/exo_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="16sp"
                tools:text="00:00:00" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:text="/"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/exo_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="16sp"
                tools:text="00:00:00" />

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/bottom"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_live_info"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="005" />

        <ImageView
            android:id="@+id/logo"
            android:layout_width="48dp"
            android:layout_height="36dp"
            android:layout_marginEnd="12dp"
            android:scaleType="fitCenter"
            android:visibility="gone" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:maxEms="48"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="民視" />

        <TextView
            android:id="@+id/play"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:layout_weight="1"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="正在播放：食神" />

        <TextView
            android:id="@+id/line"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="來源 1" />

    </LinearLayout>
</FrameLayout>