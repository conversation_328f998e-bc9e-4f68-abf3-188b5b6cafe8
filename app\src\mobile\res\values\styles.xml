<resources>

    <style name="AppTheme" parent="BaseTheme">
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="AppTheme.Live" parent="AppTheme">
        <item name="android:windowBackground">@color/black</item>
    </style>

    <style name="AppTheme.Detail" parent="AppTheme">
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:windowTranslucentStatus">false</item>
    </style>

    <style name="BaseTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primaryDark</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="bottomSheetDialogTheme">@style/BottomSheetDialog</item>
    </style>

    <style name="Control" />

    <style name="Control.Action">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:padding">8dp</item>
        <item name="android:shadowColor">@color/grey_200</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">0.5</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="BottomSheetDialog" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primaryDark</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/ModalBottomSheetDialog</item>
    </style>

    <style name="ModalBottomSheetDialog" parent="Widget.Material3.BottomSheet.Modal">
        <item name="shapeAppearance">@style/ShapeAppearance.App.LargeComponent</item>
    </style>

    <style name="ShapeAppearance.App.LargeComponent" parent="ShapeAppearance.Material3.LargeComponent">
        <item name="cornerSize">16dp</item>
    </style>

    <style name="Indicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">@color/indicator</item>
    </style>

</resources>
