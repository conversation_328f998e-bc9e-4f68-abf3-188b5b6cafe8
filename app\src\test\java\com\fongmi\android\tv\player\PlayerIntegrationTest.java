package com.fongmi.android.tv.player;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.fongmi.android.tv.Setting;

/**
 * 播放器集成测试
 * 测试ExoPlayer和MPV播放器的切换功能
 */
public class PlayerIntegrationTest {

    @Before
    public void setUp() {
        // 重置播放器设置为默认值
        Setting.putPlayer(Players.EXO);
    }

    @Test
    public void testPlayerTypeConstants() {
        // 测试播放器类型常量
        assertEquals("EXO player constant should be 0", 0, Players.EXO);
        assertEquals("MPV player constant should be 1", 1, Players.MPV);
    }

    @Test
    public void testPlayerTypeSettings() {
        // 测试播放器类型设置
        Setting.putPlayer(Players.EXO);
        assertEquals("Should get EXO player", Players.EXO, Setting.getPlayer());
        
        Setting.putPlayer(Players.MPV);
        assertEquals("Should get MPV player", Players.MPV, Setting.getPlayer());
    }

    @Test
    public void testPlayerTypeChecking() {
        // 创建一个模拟的Players实例来测试类型检查方法
        // 注意：这里需要实际的Android上下文，在单元测试中可能需要使用Robolectric
        
        // 测试EXO播放器类型检查
        Setting.putPlayer(Players.EXO);
        // assertTrue("Should be EXO player", players.isExo());
        // assertFalse("Should not be MPV player", players.isMpv());
        
        // 测试MPV播放器类型检查
        Setting.putPlayer(Players.MPV);
        // assertFalse("Should not be EXO player", players.isExo());
        // assertTrue("Should be MPV player", players.isMpv());
    }

    @Test
    public void testPlayerToggle() {
        // 测试播放器切换功能
        Setting.putPlayer(Players.EXO);
        assertEquals("Initial player should be EXO", Players.EXO, Setting.getPlayer());
        
        // 模拟切换操作
        int currentPlayer = Setting.getPlayer();
        int newPlayer = currentPlayer == Players.EXO ? Players.MPV : Players.EXO;
        Setting.putPlayer(newPlayer);
        
        assertEquals("Player should be toggled to MPV", Players.MPV, Setting.getPlayer());
        
        // 再次切换
        currentPlayer = Setting.getPlayer();
        newPlayer = currentPlayer == Players.EXO ? Players.MPV : Players.EXO;
        Setting.putPlayer(newPlayer);
        
        assertEquals("Player should be toggled back to EXO", Players.EXO, Setting.getPlayer());
    }

    @Test
    public void testPlayerTextDisplay() {
        // 测试播放器文本显示
        // 这个测试需要Android资源，在实际环境中运行
        
        Setting.putPlayer(Players.EXO);
        // String exoText = players.getPlayerText();
        // assertEquals("EXO player text should be 'EXO'", "EXO", exoText);
        
        Setting.putPlayer(Players.MPV);
        // String mpvText = players.getPlayerText();
        // assertEquals("MPV player text should be 'MPV'", "MPV", mpvText);
    }

    @Test
    public void testDefaultPlayerSetting() {
        // 测试默认播放器设置
        // 默认应该是EXO播放器
        int defaultPlayer = Setting.getPlayer();
        assertEquals("Default player should be EXO", Players.EXO, defaultPlayer);
    }
}
