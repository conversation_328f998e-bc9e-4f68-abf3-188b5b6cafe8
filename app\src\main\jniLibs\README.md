# MPV Native Libraries

## 概述

此目录用于存放MPV播放器所需的native库文件。由于libmpv库文件较大，不包含在源代码仓库中，需要单独下载。

## 所需文件

请将以下文件放置在对应的架构目录中：

### arm64-v8a/
- `libmpv.so` - MPV主库文件

### armeabi-v7a/
- `libmpv.so` - MPV主库文件

## 获取libmpv.so文件

### 方法1：从官方构建获取
1. 访问 https://github.com/mpv-android/mpv-android/releases
2. 下载最新的release版本
3. 从APK中提取libmpv.so文件
4. 将文件放置到对应的架构目录中

### 方法2：自行编译
1. 克隆 mpv-android 项目：
   ```bash
   git clone https://github.com/mpv-android/mpv-android.git
   ```
2. 按照项目说明进行编译
3. 从编译输出中获取libmpv.so文件

### 方法3：使用预编译版本
可以从以下来源获取预编译的libmpv.so：
- https://github.com/mpv-player/mpv/releases
- https://sourceforge.net/projects/mpv-player-windows/files/libmpv/

## 文件结构

正确的目录结构应该如下：
```
app/src/main/jniLibs/
├── arm64-v8a/
│   └── libmpv.so
├── armeabi-v7a/
│   └── libmpv.so
└── README.md (本文件)
```

## 注意事项

1. **版本兼容性**: 确保使用的libmpv版本与代码兼容
2. **架构匹配**: 确保.so文件与目标架构匹配
3. **文件权限**: 确保.so文件具有正确的执行权限
4. **依赖库**: libmpv可能依赖其他库，确保所有依赖都已包含

## 验证安装

安装完成后，可以通过以下方式验证：

1. 编译项目，检查是否有链接错误
2. 在设备上运行应用，检查MPV播放器是否可用
3. 查看logcat输出，确认libmpv库加载成功

## 故障排除

### 库加载失败
- 检查文件路径是否正确
- 确认架构匹配
- 检查文件权限

### 运行时错误
- 检查libmpv版本兼容性
- 确认所有依赖库都已包含
- 查看详细的错误日志

## 许可证

libmpv使用LGPL许可证，请确保遵守相关许可证要求。
