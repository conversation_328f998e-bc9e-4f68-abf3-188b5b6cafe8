<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_20">

    <LinearLayout
        android:id="@+id/top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="8dp"
            android:paddingTop="8dp"
            android:paddingEnd="8dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="marquee"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="14sp"
                tools:text="慶餘年第一季：第一集" />

            <TextView
                android:id="@+id/size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="12sp"
                tools:text="1920 x 1080" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/cast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:src="@drawable/ic_control_cast" />

            <ImageView
                android:id="@+id/info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:src="@drawable/ic_control_info" />

            <ImageView
                android:id="@+id/keep"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:src="@drawable/ic_control_keep_off" />

            <ImageView
                android:id="@+id/setting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:src="@drawable/ic_control_setting" />

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="horizontal">

        <FrameLayout
            android:id="@+id/prevRoot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_control"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/prev"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/exo_icon_previous" />

        </FrameLayout>

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:background="@drawable/shape_control">

            <ImageView
                android:id="@+id/play"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/exo_icon_play" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/nextRoot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_control"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/next"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/exo_icon_next" />

        </FrameLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/danmaku"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            android:src="@drawable/ic_control_danmaku_on" />

    </LinearLayout>

    <include
        android:id="@+id/right"
        layout="@layout/view_control_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="8dp" />

    <LinearLayout
        android:id="@+id/bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="8dp"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/parse"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:padding="8dp"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="5"
            tools:listitem="@layout/adapter_parse_dark" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <com.fongmi.android.tv.ui.custom.CustomSeekView
                android:id="@+id/seek"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/full"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_control_full" />

        </LinearLayout>

        <include
            android:id="@+id/action"
            layout="@layout/view_control_vod_action"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>
</RelativeLayout>