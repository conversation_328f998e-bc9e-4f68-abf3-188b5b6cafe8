<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:elevation="0dp"
        app:elevation="0dp"
        app:liftOnScrollColor="@color/transparent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="4dp"
            app:layout_scrollFlags="scroll|enterAlways">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_logo" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="12dp"
                android:layout_weight="1"
                android:background="@drawable/selector_item_round"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/hot"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    tools:ignore="NestedWeights"
                    tools:text="狂飆" />

                <ImageView
                    android:id="@+id/search"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_action_search" />

            </LinearLayout>

            <ImageView
                android:id="@+id/keep"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_action_keep" />

            <ImageView
                android:id="@+id/history"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_action_history" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:paddingStart="8dp"
            android:paddingTop="12dp"
            android:paddingEnd="8dp"
            android:paddingBottom="12dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/adapter_type" />

    </com.google.android.material.appbar.AppBarLayout>

    <com.fongmi.android.tv.ui.custom.CustomViewPager
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <ImageView
        android:id="@+id/retry"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_gravity="center"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_action_retry"
        android:visibility="gone" />

    <include
        android:id="@+id/progress"
        layout="@layout/view_progress"
        android:visibility="gone" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/filter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:backgroundTint="@color/blue_500"
        app:layout_anchor="@id/pager"
        app:layout_anchorGravity="end|bottom"
        app:layout_behavior="com.fongmi.android.tv.ui.custom.CustomFabBehavior"
        app:srcCompat="@drawable/ic_fab_filter"
        app:tint="@color/white"
        tools:visibility="visible" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/link"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:backgroundTint="@color/blue_500"
        app:layout_anchor="@id/pager"
        app:layout_anchorGravity="end|bottom"
        app:layout_behavior="com.fongmi.android.tv.ui.custom.CustomFabBehavior"
        app:srcCompat="@drawable/ic_fab_link"
        app:tint="@color/white"
        tools:visibility="visible" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:tag="top"
        android:visibility="invisible"
        app:backgroundTint="@color/blue_500"
        app:layout_anchor="@id/pager"
        app:layout_anchorGravity="end|bottom"
        app:layout_behavior="com.fongmi.android.tv.ui.custom.CustomFabBehavior"
        app:srcCompat="@drawable/ic_fab_top"
        app:tint="@color/white"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>