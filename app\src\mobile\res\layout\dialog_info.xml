<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true"
    android:padding="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="middle"
            android:linksClickable="false"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:autoLink="all"
            android:linksClickable="false"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="14sp" />

    </LinearLayout>
</androidx.core.widget.NestedScrollView>