package com.fongmi.android.tv.player.mpv;

import android.content.Context;
import android.net.Uri;

import androidx.media3.common.VideoSize;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * MPV播放器单元测试
 */
@RunWith(RobolectricTestRunner.class)
public class MpvPlayerTest {

    @Mock
    private MpvPlayer.Listener mockListener;

    private MpvPlayer mpvPlayer;
    private Context context;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        context = RuntimeEnvironment.getApplication();
        
        // 注意：在测试环境中，native库可能无法加载
        // 这些测试主要验证Java层的逻辑
        try {
            mpvPlayer = new MpvPlayer(context);
            mpvPlayer.setListener(mockListener);
        } catch (UnsatisfiedLinkError e) {
            // 在测试环境中忽略native库加载错误
            System.out.println("Native library not available in test environment: " + e.getMessage());
        }
    }

    @Test
    public void testInitialState() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        assertFalse("Initial playWhenReady should be false", mpvPlayer.getPlayWhenReady());
        assertFalse("Initial isPlaying should be false", mpvPlayer.isPlaying());
        assertEquals("Initial state should be IDLE", MpvPlayer.STATE_IDLE, mpvPlayer.getPlaybackState());
        assertEquals("Initial position should be 0", 0, mpvPlayer.getCurrentPosition());
        assertEquals("Initial speed should be 1.0", 1.0f, mpvPlayer.getPlaybackSpeed(), 0.01f);
    }

    @Test
    public void testSetPlayWhenReady() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        mpvPlayer.setPlayWhenReady(true);
        assertTrue("PlayWhenReady should be true", mpvPlayer.getPlayWhenReady());
        
        mpvPlayer.setPlayWhenReady(false);
        assertFalse("PlayWhenReady should be false", mpvPlayer.getPlayWhenReady());
    }

    @Test
    public void testSetMediaItem() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        Map<String, String> headers = new HashMap<>();
        headers.put("User-Agent", "TestAgent");
        headers.put("Referer", "http://example.com");
        
        Uri testUri = Uri.parse("http://example.com/test.mp4");
        
        mpvPlayer.setMediaItem(headers, testUri, "mp4", null, null, 0);
        
        // 验证状态变化
        verify(mockListener, timeout(1000)).onPlaybackStateChanged(MpvPlayer.STATE_BUFFERING);
    }

    @Test
    public void testPlaybackControls() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        // 测试播放
        mpvPlayer.play();
        assertTrue("PlayWhenReady should be true after play()", mpvPlayer.getPlayWhenReady());
        
        // 测试暂停
        mpvPlayer.pause();
        assertFalse("PlayWhenReady should be false after pause()", mpvPlayer.getPlayWhenReady());
        assertFalse("IsPlaying should be false after pause()", mpvPlayer.isPlaying());
        
        // 测试停止
        mpvPlayer.stop();
        assertFalse("PlayWhenReady should be false after stop()", mpvPlayer.getPlayWhenReady());
        assertFalse("IsPlaying should be false after stop()", mpvPlayer.isPlaying());
        assertEquals("State should be IDLE after stop()", MpvPlayer.STATE_IDLE, mpvPlayer.getPlaybackState());
    }

    @Test
    public void testSeekTo() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        long seekPosition = 30000; // 30 seconds
        mpvPlayer.seekTo(seekPosition);
        
        // 验证位置变化通知
        verify(mockListener, timeout(1000)).onPositionDiscontinuity();
    }

    @Test
    public void testSetPlaybackSpeed() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        float testSpeed = 1.5f;
        mpvPlayer.setPlaybackSpeed(testSpeed);
        assertEquals("Playback speed should be updated", testSpeed, mpvPlayer.getPlaybackSpeed(), 0.01f);
        
        testSpeed = 0.5f;
        mpvPlayer.setPlaybackSpeed(testSpeed);
        assertEquals("Playback speed should be updated", testSpeed, mpvPlayer.getPlaybackSpeed(), 0.01f);
    }

    @Test
    public void testVideoSize() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        VideoSize initialSize = mpvPlayer.getVideoSize();
        assertEquals("Initial video size should be UNKNOWN", VideoSize.UNKNOWN, initialSize);
    }

    @Test
    public void testIsCurrentMediaItemLive() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        // 默认情况下应该返回false
        assertFalse("Default media should not be live", mpvPlayer.isCurrentMediaItemLive());
    }

    @Test
    public void testRelease() {
        if (mpvPlayer == null) return; // Skip if native library not available
        
        // 测试释放资源
        mpvPlayer.release();
        
        // 释放后状态应该重置
        assertFalse("PlayWhenReady should be false after release", mpvPlayer.getPlayWhenReady());
        assertFalse("IsPlaying should be false after release", mpvPlayer.isPlaying());
        assertEquals("State should be IDLE after release", MpvPlayer.STATE_IDLE, mpvPlayer.getPlaybackState());
    }
}
