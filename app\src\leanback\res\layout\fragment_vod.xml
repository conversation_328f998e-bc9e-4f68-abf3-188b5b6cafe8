<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.fongmi.android.tv.ui.custom.CustomVerticalGridView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingStart="24dp"
        android:paddingTop="16dp"
        android:paddingEnd="24dp"
        android:paddingBottom="24dp"
        app:focusOutEnd="true"
        app:focusOutFront="true" />

    <include
        android:id="@+id/progress"
        layout="@layout/view_progress"
        android:visibility="gone" />

</FrameLayout>