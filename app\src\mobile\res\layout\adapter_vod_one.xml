<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="120dp"
    android:layout_margin="8dp"
    android:background="@drawable/shape_vod_list"
    android:foreground="@drawable/shape_vod">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image"
        android:layout_width="90dp"
        android:layout_height="match_parent"
        android:background="@color/black_10"
        android:scaleType="center"
        app:shapeAppearanceOverlay="@style/Vod.Grid"
        tools:src="@drawable/ic_img_loading" />

    <TextView
        android:id="@+id/site"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_vod_site"
        android:ellipsize="end"
        android:maxWidth="90dp"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        tools:text="泥巴"
        tools:visibility="visible" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_toEndOf="@+id/image"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:text="蜘蛛人" />

        <TextView
            android:id="@+id/remark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="1080p" />

    </LinearLayout>
</RelativeLayout>